{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 12343286838202167322, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[702346356325763017, "rmcp", false, 834809220034825675], [1188017320647144970, "async_stream", false, 17672003203704775048], [1288403060204016458, "tokio_util", false, 2101072470926673410], [2706460456408817945, "futures", false, 8274733580214397057], [4891297352905791595, "axum", false, 18071286162083432143], [8569119365930580996, "serde_json", false, 2152363156938533219], [8606274917505247608, "tracing", false, 13620241847557398666], [9689903380558560274, "serde", false, 5360501223996898984], [12944427623413450645, "tokio", false, 244415518657304090], [13625485746686963219, "anyhow", false, 13221010093326852098], [16230660778393187092, "tracing_subscriber", false, 1542538997777270956]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hello-mcp-c0898aa56e18de04\\dep-bin-hello-mcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}