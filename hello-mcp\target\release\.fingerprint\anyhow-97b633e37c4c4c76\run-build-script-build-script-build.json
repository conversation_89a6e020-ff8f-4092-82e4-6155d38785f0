{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 10997435666677109640]], "local": [{"RerunIfChanged": {"output": "release\\build\\anyhow-97b633e37c4c4c76\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}