{"rustc": 12488743700189009532, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 10369491684090452477, "path": 3562065483322521236, "deps": [[325572602735163265, "tracing_attributes", false, 11948388731729592808], [1906322745568073236, "pin_project_lite", false, 2108799129751499761], [3424551429995674438, "tracing_core", false, 9782082088208031], [5986029879202738730, "log", false, 15875835707837187817]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-62e4e3bcdab3713e\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}