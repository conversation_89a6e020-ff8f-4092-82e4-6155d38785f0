Write-Host "=== Hello MCP 简单测试 ===" -ForegroundColor Green

# 检查可执行文件
$exe_path = ".\target\release\hello-mcp.exe"
if (-not (Test-Path $exe_path)) {
    Write-Host "错误: 找不到 $exe_path" -ForegroundColor Red
    Write-Host "请运行: cargo build --release" -ForegroundColor Yellow
    exit 1
}

Write-Host "找到可执行文件: $exe_path" -ForegroundColor Green

# 测试 SSE 模式
Write-Host "`n启动 SSE 服务器..." -ForegroundColor Yellow
$process = Start-Process -FilePath $exe_path -ArgumentList "--sse" -PassThru -WindowStyle Hidden

Start-Sleep -Seconds 3

Write-Host "测试服务器..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/" -TimeoutSec 5
    Write-Host "✓ 服务器响应: $($response.Content)" -ForegroundColor Green
    
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/message" -Method POST -Body "hello_mcp" -TimeoutSec 5
    Write-Host "✓ hello_mcp 工具响应: $($response.Content.Trim())" -ForegroundColor Green
} catch {
    Write-Host "✗ 测试失败: $_" -ForegroundColor Red
}

# 停止服务器
if ($process -and !$process.HasExited) {
    Stop-Process -Id $process.Id -Force
    Write-Host "服务器已停止" -ForegroundColor Yellow
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
