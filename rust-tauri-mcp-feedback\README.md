# MCP Feedback Tool - Rust + Tauri 实现

一个基于Rust和Tauri的MCP (Model Context Protocol) 反馈工具，提供苹果风格的用户界面，支持Markdown渲染和用户反馈收集。

## 🎯 功能特性

- **MCP协议支持**: 完全符合MCP 2024-11-05规范
- **feedback工具**: 显示Markdown内容并收集用户反馈
- **苹果风格UI**: 现代化的用户界面设计
- **SQLite存储**: 持久化反馈数据
- **双模式运行**: 支持独立MCP服务器和GUI应用模式

## 🚀 快速开始

### 构建项目

```powershell
# 构建MCP服务器
cd src-tauri
cargo build --release --bin mcp_server

# 构建GUI应用
cargo build --release
```

### 启动MCP服务器

```powershell
# 使用启动脚本（推荐）
.\scripts\start-mcp-server.ps1

# 或直接运行
.\src-tauri\target\release\mcp_server.exe
```

## 📋 Augment配置

1. 打开Augment设置面板
2. 在MCP服务器部分添加：
   - **Name**: `feedback`
   - **Command**: `你的项目路径\src-tauri\target\release\mcp_server.exe`
   - **Environment**: `RUST_LOG=info`, `MCP_MODE=server`

### 使用方法

在Augment中使用feedback工具：

```
请使用feedback工具显示以下工作汇报并收集反馈：

## 今日工作完成情况
- ✅ 完成了功能开发
- ✅ 修复了相关问题
- ✅ 优化了性能表现
```

## 🛠️ feedback工具

**功能**: 显示Markdown内容并收集用户反馈

**主要参数**:
- `work_summary`: 要显示的Markdown内容
- `title`: 反馈标题（可选）
- `allow_save`: 是否允许保存反馈（可选）

## 🏗️ 项目结构

```
rust-tauri-mcp-feedback/
├── src-tauri/                 # Rust后端
│   ├── src/
│   │   ├── bin/
│   │   │   └── mcp_server.rs  # 独立MCP服务器
│   │   ├── mcp/               # MCP协议实现
│   │   ├── storage/           # 数据存储
│   │   ├── ui/                # UI命令处理
│   │   └── lib.rs             # 主库文件
│   └── Cargo.toml
├── src/                       # 前端文件
├── scripts/                   # 脚本文件
│   └── start-mcp-server.ps1   # 启动脚本
├── augment-mcp-config.json    # Augment配置
└── README.md
```

## 🔧 环境要求

- Rust 1.85.1+
- Node.js 18+
- Windows 10/11

## 📝 许可证

MIT License
