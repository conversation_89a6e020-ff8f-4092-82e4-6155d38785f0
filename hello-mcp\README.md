# Hello MCP - 简单的 MCP 服务器实现

这是一个基于 Rust 和 MCP (Model Context Protocol) SDK 的简单服务器实现，提供一个 `hello_mcp` 工具，当被调用时返回 "hello,mcp"。

## 功能特性

- 基于官方 MCP Rust SDK (rmcp) 实现
- 支持两种运行模式：
  - **stdio 模式**：标准输入输出，适合与 AI agent 直接集成
  - **SSE 模式**：Server-Sent Events HTTP 服务器，适合 Web 集成
- 提供一个简单的 `hello_mcp` 工具

## 构建项目

```bash
# 克隆或进入项目目录
cd F:\mcp\Rustmcp\hello-mcp

# 构建 release 版本
cargo build --release
```

## 运行方式

### 1. stdio 模式（默认）

```bash
# 直接运行
.\target\release\hello-mcp.exe

# 或者使用 cargo
cargo run --release
```

### 2. SSE 模式

```bash
# 使用 --sse 参数
.\target\release\hello-mcp.exe --sse

# 或者使用 cargo
cargo run --release -- --sse
```

SSE 模式会启动一个 HTTP 服务器在 `http://127.0.0.1:8000`：
- 主页：`http://127.0.0.1:8000/`
- SSE 端点：`http://127.0.0.1:8000/sse`
- 消息端点：`http://127.0.0.1:8000/message`

## 测试工具

### 测试 stdio 模式

```bash
echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/call", "params": {"name": "hello_mcp", "arguments": {}}}' | .\target\release\hello-mcp.exe
```

### 测试 SSE 模式

```bash
# 启动服务器
.\target\release\hello-mcp.exe --sse

# 在另一个终端测试
curl http://127.0.0.1:8000/
curl -X POST http://127.0.0.1:8000/message -d "hello_mcp"
```

## AI Agent 配置

### Claude Desktop 配置

在 Claude Desktop 的配置文件中添加以下配置：

**Windows** (`%APPDATA%\Claude\claude_desktop_config.json`):
```json
{
  "mcpServers": {
    "hello-mcp": {
      "command": "F:\\mcp\\Rustmcp\\hello-mcp\\target\\release\\hello-mcp.exe",
      "args": []
    }
  }
}
```

**macOS/Linux**:
```json
{
  "mcpServers": {
    "hello-mcp": {
      "command": "/path/to/hello-mcp/target/release/hello-mcp",
      "args": []
    }
  }
}
```

### 其他 AI Agent 配置

对于支持 MCP 的其他 AI agent，可以使用以下配置：

1. **可执行文件路径**：`F:\mcp\Rustmcp\hello-mcp\target\release\hello-mcp.exe`
2. **协议**：MCP (Model Context Protocol)
3. **传输方式**：stdio
4. **工具列表**：
   - `hello_mcp`: 返回 "hello,mcp" 的简单工具

## 工具说明

### hello_mcp

- **描述**：Say hello from MCP server
- **参数**：无
- **返回值**：文本 "hello,mcp"

## 开发说明

### 项目结构

```
hello-mcp/
├── src/
│   ├── main.rs           # 主程序入口
│   ├── hello_service.rs  # MCP 服务实现
│   └── sse_server.rs     # SSE 服务器实现
├── Cargo.toml           # 项目配置
└── README.md           # 说明文档
```

### 依赖项

- `rmcp`: MCP Rust SDK
- `tokio`: 异步运行时
- `axum`: Web 框架（SSE 模式）
- `tracing`: 日志记录
- `serde`: 序列化/反序列化

### 扩展开发

要添加新的工具，在 `hello_service.rs` 中：

1. 添加新的方法并使用 `#[tool]` 宏标注
2. 在 `tool_router` 中注册新工具
3. 重新构建项目

## 故障排除

1. **编译错误**：确保安装了 Rust 1.70+ 版本
2. **运行时错误**：检查端口 8000 是否被占用（SSE 模式）
3. **连接问题**：确保防火墙允许程序运行

## 许可证

MIT License
