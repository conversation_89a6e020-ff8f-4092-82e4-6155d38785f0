mod hello_service;
mod sse_server;

use anyhow::Result;
use hello_service::HelloService;
use rmcp::{ServiceExt, transport::stdio};
use tracing_subscriber::{self, EnvFilter};

#[tokio::main]
async fn main() -> Result<()> {
    let args: Vec<String> = std::env::args().collect();

    if args.len() > 1 && args[1] == "--sse" {
        // Run SSE server mode
        sse_server::start_sse_server().await
    } else {
        // Run stdio mode (default)
        run_stdio_mode().await
    }
}

async fn run_stdio_mode() -> Result<()> {
    // Initialize the tracing subscriber with file and stdout logging
    tracing_subscriber::fmt()
        .with_env_filter(EnvFilter::from_default_env().add_directive(tracing::Level::DEBUG.into()))
        .with_writer(std::io::stderr)
        .with_ansi(false)
        .init();

    tracing::info!("Starting Hello MCP server in stdio mode");

    // Create an instance of our hello service
    let service = HelloService::new().serve(stdio()).await.inspect_err(|e| {
        tracing::error!("serving error: {:?}", e);
    })?;

    service.waiting().await?;
    Ok(())
}
