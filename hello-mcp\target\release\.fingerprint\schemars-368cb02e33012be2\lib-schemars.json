{"rustc": 12488743700189009532, "features": "[\"chrono04\", \"default\", \"derive\", \"schemars_derive\", \"std\"]", "declared_features": "[\"_ui_test\", \"arrayvec07\", \"bigdecimal04\", \"bytes1\", \"chrono04\", \"default\", \"derive\", \"either1\", \"indexmap2\", \"jiff02\", \"preserve_order\", \"raw_value\", \"rust_decimal1\", \"schemars_derive\", \"semver1\", \"smallvec1\", \"smol_str02\", \"std\", \"url2\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2040997289075261528, "path": 15532315586292375889, "deps": [[6982418085031928086, "dyn_clone", false, 4276366522612089768], [8569119365930580996, "serde_json", false, 18250379096220316599], [9689903380558560274, "serde", false, 290523187581326707], [9897246384292347999, "chrono04", false, 13093579006592957916], [13535282280064720520, "ref_cast", false, 6302838406949521039], [14624475729786384435, "schemars_derive", false, 6479155603827429769]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-368cb02e33012be2\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}