{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 2937790071811063934, "profile": 1369601567987815722, "path": 12892821685721238240, "deps": [[3060637413840920116, "proc_macro2", false, 17784075560822324106], [3972868919765946583, "serde_derive_internals", false, 14874525161096743339], [4974441333307933176, "syn", false, 14094537336609968083], [17990358020177143287, "quote", false, 10218877528644644230]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars_derive-c4cdd4c595c4efe7\\dep-lib-schemars_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}