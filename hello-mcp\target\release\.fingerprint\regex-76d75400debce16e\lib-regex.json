{"rustc": 12488743700189009532, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 13594560759422763188, "deps": [[555019317135488525, "regex_automata", false, 4994531628521006763], [9408802513701742484, "regex_syntax", false, 13191436787349840195]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-76d75400debce16e\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}