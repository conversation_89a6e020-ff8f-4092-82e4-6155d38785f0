use std::net::SocketAddr;
use axum::{
    extract::State,
    http::{HeaderMap, StatusCode},
    response::Sse,
    routing::{get, post},
    Router,
};
use tokio::sync::broadcast;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

const BIND_ADDRESS: &str = "127.0.0.1:8000";

#[derive(Clone)]
pub struct AppState {
    tx: broadcast::Sender<String>,
}

pub async fn start_sse_server() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "debug".to_string().into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    tracing::info!("Starting Hello MCP SSE Server on {}", BIND_ADDRESS);

    let (tx, _rx) = broadcast::channel(100);
    let app_state = AppState { tx };

    let app = Router::new()
        .route("/sse", get(sse_handler))
        .route("/message", post(message_handler))
        .route("/", get(|| async { "Hello MCP Server is running!" }))
        .with_state(app_state);

    let addr: SocketAddr = BIND_ADDRESS.parse()?;
    let listener = tokio::net::TcpListener::bind(addr).await?;

    tracing::info!("Hello MCP Server is running!");
    tracing::info!("SSE endpoint: http://{}/sse", BIND_ADDRESS);
    tracing::info!("Message endpoint: http://{}/message", BIND_ADDRESS);
    tracing::info!("Press Ctrl+C to stop the server");

    axum::serve(listener, app.into_make_service())
        .with_graceful_shutdown(shutdown_signal())
        .await?;

    Ok(())
}

async fn sse_handler(
    State(state): State<AppState>,
    _headers: HeaderMap,
) -> Sse<impl futures::Stream<Item = Result<axum::response::sse::Event, std::convert::Infallible>>> {
    tracing::info!("SSE connection established");

    let mut rx = state.tx.subscribe();

    let stream = async_stream::stream! {
        // Send initial connection message
        yield Ok(axum::response::sse::Event::default()
            .data("Connected to Hello MCP Server"));

        // Listen for messages
        while let Ok(msg) = rx.recv().await {
            yield Ok(axum::response::sse::Event::default().data(msg));
        }
    };

    Sse::new(stream).keep_alive(
        axum::response::sse::KeepAlive::new()
            .interval(std::time::Duration::from_secs(15))
            .text("keep-alive-text"),
    )
}

async fn message_handler(
    State(state): State<AppState>,
    body: String,
) -> Result<String, StatusCode> {
    tracing::info!("Received message: {}", body);
    
    // Simple MCP tool call simulation
    if body.contains("hello_mcp") {
        let response = "hello,mcp";
        let _ = state.tx.send(response.to_string());
        Ok(response.to_string())
    } else {
        let response = "Unknown command";
        let _ = state.tx.send(response.to_string());
        Ok(response.to_string())
    }
}

async fn shutdown_signal() {
    let ctrl_c = async {
        tokio::signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    tracing::info!("signal received, starting graceful shutdown");
}
