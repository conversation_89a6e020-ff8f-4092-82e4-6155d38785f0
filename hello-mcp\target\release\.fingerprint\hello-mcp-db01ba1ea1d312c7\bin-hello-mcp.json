{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 12343286838202167322, "profile": 2040997289075261528, "path": 4942398508502643691, "deps": [[702346356325763017, "rmcp", false, 16435978816822681841], [1188017320647144970, "async_stream", false, 17963203179128779895], [1288403060204016458, "tokio_util", false, 18327065014870203172], [2706460456408817945, "futures", false, 1972872716513107917], [4891297352905791595, "axum", false, 14562393905383615151], [8569119365930580996, "serde_json", false, 18250379096220316599], [8606274917505247608, "tracing", false, 18156541316650423820], [9689903380558560274, "serde", false, 290523187581326707], [12944427623413450645, "tokio", false, 10944726148875713923], [13625485746686963219, "anyhow", false, 13002756112068798892], [16230660778393187092, "tracing_subscriber", false, 15306390656694068007]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hello-mcp-db01ba1ea1d312c7\\dep-bin-hello-mcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}