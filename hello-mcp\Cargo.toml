[package]
name = "hello-mcp"
version = "0.1.0"
edition = "2021"

[dependencies]
rmcp = { version = "0.3.1", features = ["server", "transport-io"] }
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
axum = "0.7.9"
tokio-util = "0.7"
futures = "0.3"
async-stream = "0.3"
