# Hello MCP 测试脚本
# 用于测试 hello-mcp 服务器的功能

Write-Host "=== Hello MCP 测试脚本 ===" -ForegroundColor Green

# 检查可执行文件是否存在
$exe_path = ".\target\release\hello-mcp.exe"
if (-not (Test-Path $exe_path)) {
    Write-Host "错误: 找不到可执行文件 $exe_path" -ForegroundColor Red
    Write-Host "请先运行: cargo build --release" -ForegroundColor Yellow
    exit 1
}

Write-Host "找到可执行文件: $exe_path" -ForegroundColor Green

# 测试 1: stdio 模式初始化测试
Write-Host "`n=== 测试 1: stdio 模式初始化 ===" -ForegroundColor Cyan
$init_message = @'
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"tools": {}}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}
'@

try {
    $result = echo $init_message | & $exe_path 2>&1
    if ($result -match '"result"') {
        Write-Host "✓ stdio 模式初始化成功" -ForegroundColor Green
        Write-Host "响应: $($result | Where-Object { $_ -match 'result' })" -ForegroundColor Gray
    } else {
        Write-Host "✗ stdio 模式初始化失败" -ForegroundColor Red
        Write-Host "输出: $result" -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ stdio 模式测试出错: $_" -ForegroundColor Red
}

# 测试 2: SSE 模式测试
Write-Host "`n=== 测试 2: SSE 模式 ===" -ForegroundColor Cyan

# 启动 SSE 服务器
Write-Host "启动 SSE 服务器..." -ForegroundColor Yellow
$sse_process = Start-Process -FilePath $exe_path -ArgumentList "--sse" -PassThru -WindowStyle Hidden

# 等待服务器启动
Start-Sleep -Seconds 3

try {
    # 测试主页
    Write-Host "测试主页..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/" -TimeoutSec 5
    if ($response.Content -eq "Hello MCP Server is running!") {
        Write-Host "✓ SSE 服务器主页正常" -ForegroundColor Green
    } else {
        Write-Host "✗ SSE 服务器主页异常" -ForegroundColor Red
    }

    # 测试 hello_mcp 工具
    Write-Host "测试 hello_mcp 工具..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/message" -Method POST -Body "hello_mcp" -TimeoutSec 5
    if ($response.Content.Trim() -eq "hello,mcp") {
        Write-Host "✓ hello_mcp 工具正常工作，返回: $($response.Content.Trim())" -ForegroundColor Green
    } else {
        Write-Host "✗ hello_mcp 工具异常，返回: $($response.Content)" -ForegroundColor Red
    }

    # 测试未知命令
    Write-Host "测试未知命令..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000/message" -Method POST -Body "unknown_command" -TimeoutSec 5
    if ($response.Content.Trim() -eq "Unknown command") {
        Write-Host "✓ 未知命令处理正常" -ForegroundColor Green
    } else {
        Write-Host "✗ 未知命令处理异常" -ForegroundColor Red
    }

} catch {
    Write-Host "✗ SSE 模式测试出错: $_" -ForegroundColor Red
} finally {
    # 停止 SSE 服务器
    if ($sse_process -and !$sse_process.HasExited) {
        Write-Host "停止 SSE 服务器..." -ForegroundColor Yellow
        Stop-Process -Id $sse_process.Id -Force
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有测试都通过，说明 hello-mcp 服务器工作正常！" -ForegroundColor Green
Write-Host "`n配置 AI Agent:" -ForegroundColor Cyan
Write-Host "1. 将以下路径添加到 AI agent 配置中:" -ForegroundColor White
Write-Host "   $((Get-Location).Path)\target\release\hello-mcp.exe" -ForegroundColor Yellow
Write-Host "2. 使用 stdio 传输模式" -ForegroundColor White
Write-Host "3. 可用工具: hello_mcp" -ForegroundColor White
